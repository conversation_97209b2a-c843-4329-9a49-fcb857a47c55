//+------------------------------------------------------------------+
//|                                          #TradingLabs - ROBS.mq5 |
//|                                 Copyright @ 2022, TradingLabs.id |
//|                         http://www.facebook.com/robbysuhendrawan |
//+------------------------------------------------------------------+
#property copyright "Copyright @ 2022, TradingLabs.id"
#property link      "http://www.facebook.com/robbysuhendrawan"
#property version   "1.0"
#property indicator_chart_window
#property indicator_buffers 0
#property indicator_plots   0
#property strict

// Input Parameters
input group "=== Alert Settings ==="
input bool   Use_Alert = false;     // Enable Alert
input bool   TrigerTime = false;    // Enable Time Trigger
input int    HoursTriger = 4;       // Hours Trigger

input group "=== ZigZag Settings ==="
input int    InpDepth = 50;         // Depth
input int    InpDeviation = 5;      // Deviation
input int    InpBackstep = 3;       // Backstep
input int    jumlah_ZZ = 250;       // Number of ZigZag Points
input int    breakline = 250;       // Number of High/Low Points
input bool   DRAW_ZZ = true;       // Draw ZigZag Lines

input group "=== Fibonacci Settings ==="
input int    jumlah_fibo = 300;     // Number of Fibonacci Levels
input int    lebar_fibo = 1;        // Fibonacci Width
input int    minFB_range = 300;     // Minimum Fibonacci Range
input color  FIBO_UP_color = clrSteelBlue;  // Bullish Color
input color  FIBO_DN_color = clrCrimson;    // Bearish Color

input group "=== Line Settings ==="
input ENUM_LINE_STYLE linestyle = STYLE_DOT;  // Line Style
input int    linewidth = 1;         // Line Width
input color  linecolor = clrNONE;   // ZigZag Line Color
input color  BUYlinecolor = clrNONE;// Buy Line Color
input color  SELLlinecolor = clrNONE;// Sell Line Color

input ENUM_TIMEFRAMES Static_TF = PERIOD_CURRENT;  // Timeframe

// Global Variables
datetime expired = D'2026.8.17';
datetime TM[];
double HGZZ[];
bool ATAS[];
int zzbar[];
int J;
int zn;
int fbnum = 0;
int ZZ1bar, ZZ2bar;
bool isATAS = 0;
datetime TMsig = 0;
bool waitup = 0;
bool waitdown = 0;
string NM = "";
int barfactor;
double fibolevels[] = {0, 26.4, 38.2, 50, 100, 200, 250, 300};

//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int OnInit()
{
   if(InpBackstep >= InpDepth)
   {
      Print("Backstep cannot be greater or equal to Depth");
      return(INIT_FAILED);
   }
   J = jumlah_ZZ + 1;
   barfactor = (int)MathMax(1, Static_TF/Period());
   NM = "Blend Fire " + TFNAME(Static_TF);
   ArrayResize(TM, J);
   ArrayResize(HGZZ, J);
   ArrayResize(ATAS, J);
   ArrayResize(zzbar, J);
   ArrayInitialize(TM, 0);
   ArrayInitialize(HGZZ, 0);
   ArrayInitialize(ATAS, 0);
   ArrayInitialize(zzbar, 0);
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| Custom indicator iteration function                              |
//+------------------------------------------------------------------+
int OnCalculate(const int rates_total,
                const int prev_calculated,
                const datetime &time[],
                const double &open[],
                const double &high[],
                const double &low[],
                const double &close[],
                const long &tick_volume[],
                const long &volume[],
                const int &spread[])
{
   if(TimeCurrent() > expired)
   {
      Alert("TradingLabs - ROBS indicator is EXPIRED");
      return(0);
   }
   run(time, open, high, low, close, rates_total);
   return(rates_total);
}

//+------------------------------------------------------------------+
//| Custom indicator deinitialization function                       |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   clear(NM);
}

//+------------------------------------------------------------------+
//| Main calculation function                                        |
//+------------------------------------------------------------------+
void run(const datetime &time[], const double &open[], const double &high[], const double &low[], const double &close[], int rates_total)
{
   fbnum = 0;
   int a = 0, x = 0;
   int b = rates_total - 1;
   for(x = 0; x < b; x++)
   {
      double zz = iCustom(_Symbol, Static_TF, "ZigZag", InpDepth, InpDeviation, InpBackstep, 0, x);
      if(zz > 0)
      {
         HGZZ[a] = zz;
         TM[a] = iTime(_Symbol, Static_TF, x);
         zn = a;
         double OPN = iOpen(_Symbol, Static_TF, x);
         ATAS[a] = (zz > OPN);
         zzbar[a] = x;
         if(a == 0)
         {
            isATAS = ATAS[0];
            ZZ1bar = x;
         }
         else if(a == 1)
         {
            ZZ2bar = x;
         }
         if(barfactor > 1)
         {
            int bs = iBarShift(_Symbol, 0, TM[a]);
            for(int v = bs-1; v > bs-barfactor && v >= 0; v--)
            { 
               if(HGZZ[a] == high[v] || HGZZ[a] == low[v]) 
               {
                  TM[a] = time[v]; 
                  break;
               }
            }
         }
         a++;
      }
      if(a >= J) break;
   }
   if(DRAW_ZZ) draw(high, low, rates_total);
   clear(NM + "batas");
   double HGline = 0;
   int jum = (int)MathMin(breakline, jumlah_ZZ);
   for(int s = 0; s < jum; s++)
   {
      for(x = zzbar[s]; x < zzbar[s+1]; x++)
      {
         double O = open[x];
         double C = close[x];
         double O2 = open[x+1];
         double C2 = close[x+1];
         double H2 = high[x+1];
         double L2 = low[x+1];
         if(ATAS[s] == 0 && C2 > O2 && C < O)
         {
            HGline = H2;
            gambar(x, HGline, s, C2, time, open, high, low, close, rates_total);
            break;
         }
         if(ATAS[s] == 1 && C2 < O2 && C > O)
         {
            HGline = L2;
            gambar(x, HGline, s, C2, time, open, high, low, close, rates_total);
            break;
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Draw order blocks and Fibonacci levels                           |
//+------------------------------------------------------------------+
void gambar(int x, double HG, int s, double HG2, const datetime &time[], const double &open[], const double &high[], const double &low[], const double &close[], int rates_total)
{
   datetime time1 = time[x+1];
   string n = NM + "batas" + string(s);
   ObjectCreate(0, n, OBJ_RECTANGLE, 0, 0, 0);
   ObjectSetInteger(0, n, OBJPROP_TIME, 0, time1);
   ObjectSetInteger(0, n, OBJPROP_TIME, 1, time[0] + 3*PeriodSeconds());
   ObjectSetDouble(0, n, OBJPROP_PRICE, 0, HG);
   ObjectSetDouble(0, n, OBJPROP_PRICE, 1, HG2);
   ObjectSetInteger(0, n, OBJPROP_SELECTABLE, 0);
   ObjectSetInteger(0, n, OBJPROP_WIDTH, linewidth);
   ObjectSetInteger(0, n, OBJPROP_STYLE, linestyle);
   ObjectSetInteger(0, n, OBJPROP_RAY, 0);
   if(!ATAS[s])
   {
      ObjectSetInteger(0, n, OBJPROP_COLOR, 0, BUYlinecolor);
      for(int d = x; d > 0; d--)
      {
         double open0 = open[d-1];
         double close0 = close[d];
         if(open0 > HG || close0 > HG)
         {
            ObjectSetInteger(0, n, OBJPROP_TIME, 1, time[d]);
            break;
         }
      }
   }
   else
   {
      ObjectSetInteger(0, n, OBJPROP_COLOR, 0, SELLlinecolor);
      for(int d = x; d > 0; d--)
      {
         double open0 = open[d-1];
         double close0 = close[d];
         if(open0 < HG || close0 < HG)
         {
            ObjectSetInteger(0, n, OBJPROP_TIME, 1, time[d]);
            break;
         }
      }
   }
   int tftf = Static_TF;
   if(tftf == 0) tftf = Period();
   if(MathAbs(HG - HGZZ[s]) >= minFB_range * _Point)
   {
      fbnum++;
      if(fbnum <= jumlah_fibo)
      {
         string mm = NM + "batas_fibo" + string(s);
         ObjectCreate(0, mm, OBJ_FIBO, 0, 0, 0);
         ObjectSetInteger(0, mm, OBJPROP_TIME, 0, time1);
         ObjectSetInteger(0, mm, OBJPROP_TIME, 1, time1 + lebar_fibo * tftf * 60);
         ObjectSetDouble(0, mm, OBJPROP_PRICE, 0, HG);
         ObjectSetDouble(0, mm, OBJPROP_PRICE, 1, HGZZ[s]);
         ObjectSetInteger(0, mm, OBJPROP_STYLE, linestyle);
         ObjectSetInteger(0, mm, OBJPROP_COLOR, 0, ATAS[s] ? FIBO_DN_color : FIBO_UP_color);
         ObjectSetInteger(0, mm, OBJPROP_RAY, 0);
         ObjectSetInteger(0, mm, OBJPROP_SELECTABLE, 1);
         int sz = ArraySize(fibolevels);
         for(int z = 0; z < sz; z++)
         {
            ObjectSetDouble(0, mm, OBJPROP_LEVELVALUE, z, fibolevels[z]/100);
            ObjectSetString(0, mm, OBJPROP_LEVELTEXT, z, DoubleToString(fibolevels[z], 1));
            ObjectSetInteger(0, mm, OBJPROP_LEVELCOLOR, z, ATAS[s] ? FIBO_DN_color : FIBO_UP_color);
            ObjectSetInteger(0, mm, OBJPROP_LEVELWIDTH, z, 1);
            ObjectSetInteger(0, mm, OBJPROP_LEVELSTYLE, z, linestyle);
         }
      }
   }
   if(s > 0) return;
   static int jamtele = -1;
   if(TrigerTime)
   {
      int hour = (int)(((ulong)TimeCurrent() / 3600) % 24) % HoursTriger;
      if(hour == jamtele) return;
      jamtele = hour;
   }
   if(isATAS == 1)
   {
      if(open[0] < HG || close[1] < HG)
      {
         if(TMsig != time[ZZ1bar])
         {
            string txt = _Symbol + " Signal SELL " + NM;
            waitup = 1;
            waitdown = 0;
            TMsig = time[ZZ1bar];
            if(Use_Alert) Alert(txt);
         }
      }
   }
   else if(isATAS == 0)
   {
      if(open[0] > HG || close[1] > HG)
      {
         if(TMsig != time[ZZ1bar])
         {
            string txt = _Symbol + " Signal BUY " + NM;
            waitdown = 1;
            waitup = 0;
            TMsig = time[ZZ1bar];
            if(Use_Alert) Alert(txt);
         }
      }
   }
}

//+------------------------------------------------------------------+
//| Draw ZigZag lines                                                |
//+------------------------------------------------------------------+
void draw(const double &high[], const double &low[], int rates_total)
{
   for(int q = 1; q <= zn; q++)
   {
      string n = NM + "_" + string(q);
      ObjectCreate(0, n, OBJ_TREND, 0, 0, 0);
      ObjectSetInteger(0, n, OBJPROP_TIME, 0, TM[q]);
      ObjectSetInteger(0, n, OBJPROP_TIME, 1, TM[q-1]);
      ObjectSetDouble(0, n, OBJPROP_PRICE, 0, HGZZ[q]);
      ObjectSetDouble(0, n, OBJPROP_PRICE, 1, HGZZ[q-1]);
      ObjectSetInteger(0, n, OBJPROP_SELECTABLE, 0);
      ObjectSetInteger(0, n, OBJPROP_WIDTH, linewidth);
      ObjectSetInteger(0, n, OBJPROP_STYLE, linestyle);
      ObjectSetInteger(0, n, OBJPROP_COLOR, 0, linecolor);
      ObjectSetInteger(0, n, OBJPROP_RAY, 0);
   }
}

//+------------------------------------------------------------------+
//| Clear objects by name prefix                                     |
//+------------------------------------------------------------------+
void clear(string name)
{
   int total = ObjectsTotal(0, -1, -1);
   for(int a = total - 1; a >= 0; a--)
   {
      string n = ObjectName(0, a, -1, -1);
      if(StringFind(n, name, 0) > -1)
         ObjectDelete(0, n);
   }
}

//+------------------------------------------------------------------+
//| Get timeframe name                                               |
//+------------------------------------------------------------------+
string TFNAME(int tf = 0)
{
   if(tf == 0) tf = Period();
   string tfs = "M" + string(tf);
   switch(tf)
   {
      case PERIOD_H1:  tfs = "H1"; break;
      case PERIOD_H4:  tfs = "H4"; break;
      case PERIOD_D1:  tfs = "D1"; break;
      case PERIOD_W1:  tfs = "W1"; break;
      case PERIOD_MN1: tfs = "MN"; break;
   }
   return(tfs);
}
