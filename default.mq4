//+------------------------------------------------------------------+
//|                                          #TradingLabs - ROBS.mq4 |
//|                                 Copyright @ 2022, TradingLabs.id |
//|                         http://www.facebook.com/robbysuhendrawan |
//+------------------------------------------------------------------+
#property copyright "Copyright @ 2022, TradingLabs.id"
#property link      "http://www.facebook.com/robbysuhendrawan"
#property version   "1.0"
#property  indicator_chart_window
#property strict

datetime expired=D'2026.8.17';
bool TrigerTime =False;
int HoursTriger = 4 ;

//---- indicator parameters
input bool Use_Alert=0; // Reversal Order Block Structure Alert
input  int InpDepth=50;  // KEY   
int InpDeviation=5;  // Deviation
int InpBackstep=3;   // Backstep
 ENUM_TIMEFRAMES Static_TF=0;
 int jumlah_ZZ=250; // JumlahZZ
 int breakline=250; // JumlahHL
input int jumlah_fibo=300; // Show ROBS.
 int lebar_fibo=1; // Lebar ROBS.
input int minFB_range=300; // Min Range Point ROBS.
 bool DRAW_ZZ=0;
 color linecolor=clrNONE; // Zline color
 color BUYlinecolor=clrNONE;//HIGH line color
 color SELLlinecolor=clrNONE; // LOW line color
input color FIBO_UP_color=clrSteelBlue; // Bullish ROBS.
input color FIBO_DN_color=clrCrimson; // Bearish ROBS.
input ENUM_LINE_STYLE  linestyle=STYLE_DOT; // Line Style
 int  linewidth=1;
/*
string text00="SNDLite";
string text11="/";
int x1=25;
int y1=15;
int x2=170;
int y2=15;
int x3=152;
int y3=8;
*/
//--- global variables
//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
  {
/*   ObjectCreate("WaterMarkTop", OBJ_LABEL, 0, 0, 0);
   ObjectSet("WaterMarkTop", OBJPROP_CORNER, 2);
   ObjectSet("WaterMarkTop", OBJPROP_XDISTANCE, x1);
   ObjectSet("WaterMarkTop", OBJPROP_YDISTANCE, y1);

   ObjectCreate("WaterMarkBottom", OBJ_LABEL, 0, 0, 0);
   ObjectSet("WaterMarkBottom", OBJPROP_CORNER, 2);
   ObjectSet("WaterMarkBottom", OBJPROP_XDISTANCE, x2);
   ObjectSet("WaterMarkBottom", OBJPROP_YDISTANCE, y2);

   ObjectCreate("WaterMarkBottom1", OBJ_LABEL, 0, 0, 0);
   ObjectSet("WaterMarkBottom1", OBJPROP_CORNER, 2);
   ObjectSet("WaterMarkBottom1", OBJPROP_XDISTANCE, x3);
   ObjectSet("WaterMarkBottom1", OBJPROP_YDISTANCE, y3);
*/

   J=jumlah_ZZ+1;
   barfactor=fmax(1, Static_TF/Period());
   if(InpBackstep>=InpDepth)
     {
      Print("Backstep cannot be greater or equal to Depth");
      return(INIT_FAILED);
     }
   NM="Blend Fire "+TFNAME(Static_TF);
// IndicatorShortName(NM);
   ArrayResize(TM, J);
   ArrayResize(HGZZ, J);
   ArrayResize(ATAS, J);
   ArrayResize(zzbar, J);
   ArrayInitialize(TM, 0);
   ArrayInitialize(HGZZ, 0);
   ArrayInitialize(ATAS, 0);
   ArrayInitialize(zzbar, 0);
   double zz=iCustom(NULL, Static_TF, "ZigZag", InpDepth, InpDeviation, InpBackstep, 0, 1);

   start();
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
  {

   clear(NM);
   //ObjectDelete("WaterMarkTop");
   //ObjectDelete("WaterMarkBottom");
   //ObjectDelete("WaterMarkBottom1");
  }
//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void start()
  {
   if(TimeCurrent()>expired)
     {
      Alert("TradingLabs - ROBS indicator is EXPIRED");
      ExpertRemove();
      return;
     }


   run();
/*   string per="M1";
   if(Period()==PERIOD_MN1)
      per="Monthly";
   if(Period()==PERIOD_W1)
      per="Weekly";
   if(Period()==PERIOD_D1)
      per="Daily";
   if(Period()==PERIOD_H4)
      per="H4";
   if(Period()==PERIOD_H1)
      per="H1";
   if(Period()==PERIOD_M30)
      per="M30";
   if(Period()==PERIOD_M15)
      per="M15";
   if(Period()==PERIOD_M5)
      per="M5";
   if(Period()==PERIOD_M1)
      per="M1";

   ObjectSetText("WaterMarkTop", text00, 20, "Arial Black", C'132,132,132');
   ObjectSet("WaterMarkTop", OBJPROP_BACK, true);
   ObjectSetText("WaterMarkBottom", Symbol()+", "+per, 20, "Arial Black", LightGray);
   ObjectSet("WaterMarkBottom", OBJPROP_BACK, true);
   ObjectSetText("WaterMarkBottom1", text11, 27, "Arial Black", Gold);
   ObjectSet("WaterMarkBottom1", OBJPROP_BACK, true);
*/


  }
//+------------------------------------------------------------------+


//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int J;
datetime TM[];
double HGZZ[];
string NM="";
int barfactor;

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
int zn;
int fbnum=0;
bool ATAS[];
void run()
  {
// if(Bars<InpDepth*barfactor*(jumlah_ZZ+2))return;
   fbnum=0;
   int a=0, x=0;
   int b=iBars(NULL, Static_TF)-1; //Alert(b); //return;

   for(x=0; x<b; x++)
     {
      double zz=iCustom(NULL, Static_TF, "ZigZag", InpDepth, InpDeviation, InpBackstep, 0, x);
      if(zz>0)
        {
         HGZZ[a]=zz;
         TM[a]=iTime(NULL, Static_TF, x);
         zn=a;
         double OPN= iOpen(NULL, Static_TF, x);
         if(zz>OPN)
            ATAS[a]=1;
         else
            ATAS[a]=0;
         zzbar[a]=x;
         if(a==0)
           {
            isATAS=ATAS[0];
            ZZ1bar=x;
           }
         if(a==1)
           {
            ZZ2bar=x;
           }
         if(barfactor>1)
           {
            int bs=iBarShift(NULL, 0, TM[a]);
            for(int v=bs-1; v>bs-barfactor&&v>=0; v--)
              { if(HGZZ[a]==High[v]||HGZZ[a]==Low[v]) {TM[a]=Time[v]; break;}}

           }
         a++;

        }


      if(a>=J)
         break;

     }
   if(DRAW_ZZ)draw();
   clear(NM+"batas");
   double HGline=0;
   int jum=fmin(breakline, jumlah_ZZ);
   for(int s=0; s<jum; s++)
     {
      for(x=zzbar[s]; x<zzbar[s+1]; x++)
        {
         double O=iOpen(NULL, Static_TF, x);
         double C=iClose(NULL, Static_TF, x);
         double H=iHigh(NULL, Static_TF, x);
         double L=iLow(NULL, Static_TF, x);
         double O2=iOpen(NULL, Static_TF, x+1);
         double C2=iClose(NULL, Static_TF, x+1);
         double H2=iHigh(NULL, Static_TF, x+1);
         double L2=iLow(NULL, Static_TF, x+1);

         if(ATAS[s]==0)
            if(C2>O2 && C<O)
              {
               HGline=H2;
               gambar(x, HGline, s, C2);
               break;
              }

         if(ATAS[s]==1)
            if(C2<O2 && C>O)
              {
               HGline=L2;
               gambar(x, HGline, s, C2);
               break;
              }

        }
     }

  }
int ZZ1bar, ZZ2bar;
int zzbar[];
bool isATAS=0;

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+

double fibolevels[]= { 0, 26.4, 38.2, 50, 100, 200, 250, 300}; //  silahkan tambah level fibo di sini
void gambar(int x, double HG, int s, double HG2)
  {
//datetime TM=

//input int jumlah_fibo=10; // Jumlah FIBO
//input int lebar_fibo=5; // lebar FIBO
//input int minFB_range=10; // min FIBO range point
   datetime time1=iTime(NULL, Static_TF, x+1);
   string n=NM+"batas"+string(s);
   ObjectCreate(0, n, OBJ_RECTANGLE, 0, 0, 0);
   ObjectSetInteger(0, n, OBJPROP_TIME1, time1);
   ObjectSetInteger(0, n, OBJPROP_TIME2, Time[0]+3*PeriodSeconds());
   ObjectSetDouble(0, n, OBJPROP_PRICE1, HG);
   ObjectSetDouble(0, n, OBJPROP_PRICE2, HG2);
   ObjectSetInteger(0, n, OBJPROP_SELECTABLE, 0);
   ObjectSetInteger(0, n, OBJPROP_WIDTH, linewidth);
   ObjectSetInteger(0, n, OBJPROP_STYLE, linestyle);
   ObjectSetInteger(0, n, OBJPROP_RAY, 0);
   if(!ATAS[s])
     {
      ObjectSetInteger(0, n, OBJPROP_COLOR, BUYlinecolor);
      for(int d=x; d>0; d--)
        {
         double open0=iOpen(NULL, Static_TF, d-1);
         double close=iClose(NULL, Static_TF, d);
         if(open0>HG || close>HG)
           {ObjectSetInteger(0, n, OBJPROP_TIME2, iTime(NULL, Static_TF, d)); break;}
        }

     }
   if(ATAS[s])
     {
      ObjectSetInteger(0, n, OBJPROP_COLOR, SELLlinecolor);
      for(int d=x; d>0; d--)
        {
         double open0=iOpen(NULL, Static_TF, d-1);
         double close=iClose(NULL, Static_TF, d);
         if(open0<HG || close<HG)
           {ObjectSetInteger(0, n, OBJPROP_TIME2, iTime(NULL, Static_TF, d)); break;}
        }

     }


   int tftf=Static_TF;
   if(tftf==0)
      tftf=Period();

   if(MathAbs(HG-HGZZ[s])>=minFB_range*Point)
     {
      fbnum++;
      if(fbnum<=jumlah_fibo)
        {
         string mm=NM+"batas_fibo"+string(s) ;
         ObjectCreate(0, mm, OBJ_FIBO, 0, 0, 0, 0, 0);
         ObjectSet(mm, OBJPROP_TIME1, time1);
         ObjectSet(mm, OBJPROP_TIME2, time1+lebar_fibo*tftf*60);
         ObjectSet(mm, OBJPROP_PRICE1, HG);
         ObjectSet(mm, OBJPROP_PRICE2, HGZZ[s]);
         ObjectSet(mm, OBJPROP_STYLE, linestyle);
         ObjectSet(mm, OBJPROP_COLOR, clrNONE);
         ObjectSet(mm, OBJPROP_RAY, 0);
         ObjectSet(mm, OBJPROP_SELECTABLE, 1); 
         if(ATAS[s])ObjectSet(mm, OBJPROP_LEVELCOLOR, FIBO_DN_color);
         else ObjectSet(mm, OBJPROP_LEVELCOLOR, FIBO_UP_color);
         ObjectSet(mm, OBJPROP_LEVELWIDTH, 1);
         ObjectSet(mm, OBJPROP_LEVELSTYLE, linestyle);

         int sz=ArraySize(fibolevels);
         ObjectSet(mm, OBJPROP_FIBOLEVELS, sz);
         for(int z=0; z<sz; z++)
           {
            ObjectSet(mm, OBJPROP_FIRSTLEVEL+z, fibolevels[z]/100);
            ObjectSetFiboDescription(mm, z, DoubleToStr(fibolevels[z], 1));
           }
        }
     }




   if(s>0)
      return;


//input bool TrigerTime =true;
//input int HoursTriger = 4 ;
   static int jamtele=-1;
   if(TrigerTime)
     {
      int hour=Hour()%HoursTriger;
      if(hour==jamtele)
         return;
      jamtele=hour;
     }


   string txt="";
   if(isATAS==1)
      if(iOpen(NULL, Static_TF, 0)<HG || iClose(NULL, Static_TF, 1)<HG)
         if(TMsig!=iTime(NULL, Static_TF, ZZ1bar))
           {
            txt=_Symbol+" Signal SELL "+ NM;
            waitup=1;
            waitdown=0;
            TMsig=iTime(NULL, Static_TF, ZZ1bar);
            if(Use_Alert)
            Alert(txt);
           }
   if(isATAS==0)
      if(iOpen(NULL, Static_TF, 0)>HG || iClose(NULL, Static_TF, 1)>HG)
         if(TMsig!=iTime(NULL, Static_TF, ZZ1bar))
           {
            txt=_Symbol+" Signal BUY "+NM;
            waitdown=1;
            waitup=0;
            TMsig=iTime(NULL, Static_TF, ZZ1bar);
            if(Use_Alert)
            Alert(txt);
           }
/*
   if(waitup)
      if(iOpen(NULL, Static_TF, 1)<iClose(NULL, Static_TF, 1))
        {
         txt=_Symbol+"  "+TFNAME(Static_TF)+" GOING BACK UP " ;
         waitup=0;
         if(Use_Alert)
            Alert(txt);
        }

   if(waitdown)
      if(iOpen(NULL, Static_TF, 1)>iClose(NULL, Static_TF, 1))
        {
         txt=_Symbol+"  "+TFNAME(Static_TF)+" GOING BACK DOWN " ;
         waitdown=0;
         if(Use_Alert)
            Alert(txt);
        }
 */
  }
datetime TMsig=0;
bool waitup=0;
bool  waitdown=0;
//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
void draw()
  {
   for(int q=1; q<=zn; q++)
     {
      /* if(barfactor>1)
      { int bs=iBarShift(NULL,0,TM[q-1]);
        for(int v=bs-1;v>bs-barfactor;v--)
       { if(HGZZ[q-1]==High[v]||HGZZ[q-1]==Low[v]){TM[q-1]=Time[v];break;}}

      }*/
      string n=NM+"_"+string(q);
      ObjectCreate(0, n, OBJ_TREND, 0, 0, 0);
      ObjectSetInteger(0, n, OBJPROP_TIME1, TM[q]);
      ObjectSetInteger(0, n, OBJPROP_TIME2, TM[q-1]);
      ObjectSetDouble(0, n, OBJPROP_PRICE1, HGZZ[q]);
      ObjectSetDouble(0, n, OBJPROP_PRICE2, HGZZ[q-1]);
      ObjectSetInteger(0, n, OBJPROP_SELECTABLE, 0);
      ObjectSetInteger(0, n, OBJPROP_WIDTH, linewidth);
      ObjectSetInteger(0, n, OBJPROP_STYLE, linestyle);
      ObjectSetInteger(0, n, OBJPROP_COLOR, linecolor);
      ObjectSetInteger(0, n, OBJPROP_RAY, 0);

     }
  }


//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
void clear(string name)
  {
   for(int a=ObjectsTotal()-1; a>=0; a--)
     {
      string n=ObjectName(a);
      if(StringFind(n, name, 0)>-1)
         ObjectDelete(n);
     }
  }

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
string TFNAME(int tf=0)
  {
   if(tf==0)
      tf=Period();

   string tfs="M"+string(tf);
   switch(tf)
     {
      case PERIOD_H1:
         tfs="H1"  ;
         break;
      case PERIOD_H4:
         tfs="H4"  ;
         break;
      case PERIOD_D1:
         tfs="D1"  ;
         break;
      case PERIOD_W1:
         tfs="W1"  ;
         break;
      case PERIOD_MN1:
         tfs="MN";
     }
   return(tfs);
  }
//+------------------------------------------------------------------+
