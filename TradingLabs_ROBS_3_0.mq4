//+------------------------------------------------------------------+
//|                                          #TradingLabs - ROBS.mq4 |
//|                                 Copyright @ 2022, TradingLabs.id |
//|                         http://www.facebook.com/robbysuhendrawan |
//+------------------------------------------------------------------+
#property copyright "Copyright @ 2022, TradingLabs.id"
#property link      "http://www.facebook.com/robbysuhendrawan"
#property version   "1.0"
#property  indicator_chart_window
#property strict

// Input parameters
input bool Use_Alert = false;        // Reversal Order Block Structure Alert
input int InpDepth = 50;             // KEY   
input int InpDeviation = 5;          // Deviation
input int InpBackstep = 3;           // Backstep
input ENUM_TIMEFRAMES Static_TF = 0; // Timeframe
input int jumlah_ZZ = 250;           // JumlahZZ
input int breakline = 250;           // JumlahHL
input int jumlah_fibo = 300;         // Show ROBS
input int lebar_fibo = 1;            // Lebar ROBS
input int minFB_range = 300;         // Min Range Point ROBS
input bool DRAW_ZZ = false;          // Draw ZigZag
input color linecolor = clrNONE;     // Zline color
input color BUYlinecolor = clrNONE;  // HIGH line color
input color SELLlinecolor = clrNONE; // LOW line color
input color FIBO_UP_color = clrSteelBlue;  // Bullish ROBS
input color FIBO_DN_color = clrCrimson;    // Bearish ROBS
input ENUM_LINE_STYLE linestyle = STYLE_DOT; // Line Style
input int linewidth = 1;             // Line Width

datetime expired=D'2026.8.17';
bool TrigerTime =false;
int HoursTriger = 4 ;

//--- global variables
//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int init()
  {
/*   ObjectCreate("WaterMarkTop", OBJ_LABEL, 0, 0, 0);
   ObjectSet("WaterMarkTop", OBJPROP_CORNER, 2);
   ObjectSet("WaterMarkTop", OBJPROP_XDISTANCE, x1);
   ObjectSet("WaterMarkTop", OBJPROP_YDISTANCE, y1);

   ObjectCreate("WaterMarkBottom", OBJ_LABEL, 0, 0, 0);
   ObjectSet("WaterMarkBottom", OBJPROP_CORNER, 2);
   ObjectSet("WaterMarkBottom", OBJPROP_XDISTANCE, x2);
   ObjectSet("WaterMarkBottom", OBJPROP_YDISTANCE, y2);

   ObjectCreate("WaterMarkBottom1", OBJ_LABEL, 0, 0, 0);
   ObjectSet("WaterMarkBottom1", OBJPROP_CORNER, 2);
   ObjectSet("WaterMarkBottom1", OBJPROP_XDISTANCE, x3);
   ObjectSet("WaterMarkBottom1", OBJPROP_YDISTANCE, y3);
*/

   J=jumlah_ZZ+1;
   barfactor=MathMax(1, Static_TF/Period());
   if(InpBackstep>=InpDepth)
     {
      Print("Backstep cannot be greater or equal to Depth");
      return(INIT_FAILED);
     }
   NM="Blend Fire "+TFNAME(Static_TF);
// IndicatorShortName(NM);
   ArrayResize(TM, J);
   ArrayResize(HGZZ, J);
   ArrayResize(ATAS, J);
   ArrayResize(zzbar, J);
   ArrayInitialize(TM, 0);
   ArrayInitialize(HGZZ, 0);
   ArrayInitialize(ATAS, false);
   ArrayInitialize(zzbar, 0);
   double zz=iCustom(NULL, Static_TF, "ZigZag", InpDepth, InpDeviation, InpBackstep, 0, 1);

   start();
   return(INIT_SUCCEEDED);
  }
//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void deinit()
  {

   clear(NM);
   //ObjectDelete("WaterMarkTop");
   //ObjectDelete("WaterMarkBottom");
   //ObjectDelete("WaterMarkBottom1");
  }
//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
int start()
  {
   if(TimeCurrent()>expired)
     {
      Alert("TradingLabs - ROBS indicator is EXPIRED");
      return(0);
     }


   run();
/*   string per="M1";
   if(Period()==PERIOD_MN1)
      per="Monthly";
   if(Period()==PERIOD_W1)
      per="Weekly";
   if(Period()==PERIOD_D1)
      per="Daily";
   if(Period()==PERIOD_H4)
      per="H4";
   if(Period()==PERIOD_H1)
      per="H1";
   if(Period()==PERIOD_M30)
      per="M30";
   if(Period()==PERIOD_M15)
      per="M15";
   if(Period()==PERIOD_M5)
      per="M5";
   if(Period()==PERIOD_M1)
      per="M1";

   ObjectSetText("WaterMarkTop", text00, 20, "Arial Black", C'132,132,132');
   ObjectSet("WaterMarkTop", OBJPROP_BACK, true);
   ObjectSetText("WaterMarkBottom", Symbol()+", "+per, 20, "Arial Black", LightGray);
   ObjectSet("WaterMarkBottom", OBJPROP_BACK, true);
   ObjectSetText("WaterMarkBottom1", text11, 27, "Arial Black", Gold);
   ObjectSet("WaterMarkBottom1", OBJPROP_BACK, true);
*/


  }
//+------------------------------------------------------------------+


//+------------------------------------------------------------------+
//| Custom indicator initialization function                         |
//+------------------------------------------------------------------+
int zn;
int fbnum=0;
bool ATAS[];
void run()
  {
   fbnum = 0;
   int a = 0, x = 0;
   int b = iBars(NULL, Static_TF) - 1;

   for(x = 0; x < b; x++)
   {
      double zz = iCustom(NULL, Static_TF, "ZigZag", InpDepth, InpDeviation, InpBackstep, 0, x);
      if(zz > 0)
      {
         HGZZ[a] = zz;
         TM[a] = iTime(NULL, Static_TF, x);
         zn = a;
         double OPN = iOpen(NULL, Static_TF, x);
         ATAS[a] = (zz > OPN);
         zzbar[a] = x;
         
         if(a == 0)
         {
            isATAS = ATAS[0];
            ZZ1bar = x;
         }
         if(a == 1)
         {
            ZZ2bar = x;
         }
         
         if(barfactor > 1)
         {
            int bs = iBarShift(NULL, 0, TM[a]);
            for(int v = bs-1; v > bs-barfactor && v >= 0; v--)
            {
               if(HGZZ[a] == High[v] || HGZZ[a] == Low[v])
               {
                  TM[a] = Time[v];
                  break;
               }
            }
         }
         a++;
      }

      if(a >= J)
         break;
   }
   
   if(DRAW_ZZ)
      draw();
      
   clear(NM + "batas");
   double HGline = 0;
   int jum = MathMin(breakline, jumlah_ZZ);
   
   for(int s = 0; s < jum; s++)
   {
      for(x = zzbar[s]; x < zzbar[s+1]; x++)
      {
         double O = iOpen(NULL, Static_TF, x);
         double C = iClose(NULL, Static_TF, x);
         double O2 = iOpen(NULL, Static_TF, x+1);
         double C2 = iClose(NULL, Static_TF, x+1);

         if(!ATAS[s] && C2 > O2 && C < O)
         {
            HGline = iHigh(NULL, Static_TF, x+1);
            gambar(x, HGline, s, C2);
            break;
         }

         if(ATAS[s] && C2 < O2 && C > O)
         {
            HGline = iLow(NULL, Static_TF, x+1);
            gambar(x, HGline, s, C2);
            break;
         }
      }
   }
  }
int ZZ1bar, ZZ2bar;
int zzbar[];
bool isATAS=false;

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+

double fibolevels[]= { 0, 26.4, 38.2, 50, 100, 200, 250, 300}; //  silahkan tambah level fibo di sini
void gambar(int x, double HG, int s, double HG2)
  {
   datetime time1 = iTime(NULL, Static_TF, x+1);
   string n = NM + "batas" + string(s);
   
   ObjectCreate(0, n, OBJ_RECTANGLE, 0, 0, 0);
   ObjectSetInteger(0, n, OBJPROP_TIME1, time1);
   ObjectSetInteger(0, n, OBJPROP_TIME2, Time[0] + 3*PeriodSeconds());
   ObjectSetDouble(0, n, OBJPROP_PRICE1, HG);
   ObjectSetDouble(0, n, OBJPROP_PRICE2, HG2);
   ObjectSetInteger(0, n, OBJPROP_SELECTABLE, false);
   ObjectSetInteger(0, n, OBJPROP_WIDTH, linewidth);
   ObjectSetInteger(0, n, OBJPROP_STYLE, linestyle);
   ObjectSetInteger(0, n, OBJPROP_RAY, false);
   
   if(!ATAS[s])
   {
      ObjectSetInteger(0, n, OBJPROP_COLOR, BUYlinecolor);
      for(int d = x; d > 0; d--)
      {
         double open0 = iOpen(NULL, Static_TF, d-1);
         double close = iClose(NULL, Static_TF, d);
         if(open0 > HG || close > HG)
         {
            ObjectSetInteger(0, n, OBJPROP_TIME2, iTime(NULL, Static_TF, d));
            break;
         }
      }
   }
   
   if(ATAS[s])
   {
      ObjectSetInteger(0, n, OBJPROP_COLOR, SELLlinecolor);
      for(int d = x; d > 0; d--)
      {
         double open0 = iOpen(NULL, Static_TF, d-1);
         double close = iClose(NULL, Static_TF, d);
         if(open0 < HG || close < HG)
         {
            ObjectSetInteger(0, n, OBJPROP_TIME2, iTime(NULL, Static_TF, d));
            break;
         }
      }
   }

   int tftf = Static_TF;
   if(tftf == 0)
      tftf = Period();

   if(MathAbs(HG - HGZZ[s]) >= minFB_range * Point)
   {
      fbnum++;
      if(fbnum <= jumlah_fibo)
      {
         string mm = NM + "batas_fibo" + string(s);
         ObjectCreate(0, mm, OBJ_FIBO, 0, 0, 0, 0, 0);
         ObjectSet(mm, OBJPROP_TIME1, time1);
         ObjectSet(mm, OBJPROP_TIME2, time1 + lebar_fibo * tftf * 60);
         ObjectSet(mm, OBJPROP_PRICE1, HG);
         ObjectSet(mm, OBJPROP_PRICE2, HGZZ[s]);
         ObjectSet(mm, OBJPROP_STYLE, linestyle);
         ObjectSet(mm, OBJPROP_COLOR, clrNONE);
         ObjectSet(mm, OBJPROP_RAY, false);
         ObjectSet(mm, OBJPROP_SELECTABLE, true);
         
         if(ATAS[s])
            ObjectSet(mm, OBJPROP_LEVELCOLOR, FIBO_DN_color);
         else 
            ObjectSet(mm, OBJPROP_LEVELCOLOR, FIBO_UP_color);
            
         ObjectSet(mm, OBJPROP_LEVELWIDTH, 1);
         ObjectSet(mm, OBJPROP_LEVELSTYLE, linestyle);

         int sz = ArraySize(fibolevels);
         ObjectSet(mm, OBJPROP_FIBOLEVELS, sz);
         for(int z = 0; z < sz; z++)
         {
            ObjectSet(mm, OBJPROP_FIRSTLEVEL + z, fibolevels[z]/100);
            ObjectSetFiboDescription(mm, z, DoubleToStr(fibolevels[z], 1));
         }
      }
   }

   if(s > 0)
      return;

   static int jamtele = -1;
   if(TrigerTime)
   {
      int hour = Hour() % HoursTriger;
      if(hour == jamtele)
         return;
      jamtele = hour;
   }

   string txt = "";
   if(isATAS && (iOpen(NULL, Static_TF, 0) < HG || iClose(NULL, Static_TF, 1) < HG))
   {
      if(TMsig != iTime(NULL, Static_TF, ZZ1bar))
      {
         txt = _Symbol + " Signal SELL " + NM;
         waitup = true;
         waitdown = false;
         TMsig = iTime(NULL, Static_TF, ZZ1bar);
         if(Use_Alert)
            Alert(txt);
      }
   }
   
   if(!isATAS && (iOpen(NULL, Static_TF, 0) > HG || iClose(NULL, Static_TF, 1) > HG))
   {
      if(TMsig != iTime(NULL, Static_TF, ZZ1bar))
      {
         txt = _Symbol + " Signal BUY " + NM;
         waitdown = true;
         waitup = false;
         TMsig = iTime(NULL, Static_TF, ZZ1bar);
         if(Use_Alert)
            Alert(txt);
      }
   }
  }
datetime TMsig=0;
bool waitup=false;
bool  waitdown=false;
//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
void draw()
  {
   for(int q = 1; q <= zn; q++)
   {
      string n = NM + "_" + string(q);
      ObjectCreate(0, n, OBJ_TREND, 0, 0, 0);
      ObjectSetInteger(0, n, OBJPROP_TIME1, TM[q]);
      ObjectSetInteger(0, n, OBJPROP_TIME2, TM[q-1]);
      ObjectSetDouble(0, n, OBJPROP_PRICE1, HGZZ[q]);
      ObjectSetDouble(0, n, OBJPROP_PRICE2, HGZZ[q-1]);
      ObjectSetInteger(0, n, OBJPROP_SELECTABLE, false);
      ObjectSetInteger(0, n, OBJPROP_WIDTH, linewidth);
      ObjectSetInteger(0, n, OBJPROP_STYLE, linestyle);
      ObjectSetInteger(0, n, OBJPROP_COLOR, linecolor);
      ObjectSetInteger(0, n, OBJPROP_RAY, false);
   }
  }


//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
void clear(string name)
  {
   for(int a = ObjectsTotal() - 1; a >= 0; a--)
   {
      string n = ObjectName(a);
      if(StringFind(n, name, 0) > -1)
         ObjectDelete(n);
   }
  }

//+------------------------------------------------------------------+
//|                                                                  |
//+------------------------------------------------------------------+
string TFNAME(int tf = 0)
  {
   if(tf == 0)
      tf = Period();

   string tfs = "M" + string(tf);
   switch(tf)
     {
      case PERIOD_H1:  tfs = "H1";  break;
      case PERIOD_H4:  tfs = "H4";  break;
      case PERIOD_D1:  tfs = "D1";  break;
      case PERIOD_W1:  tfs = "W1";  break;
      case PERIOD_MN1: tfs = "MN";  break;
     }
   return(tfs);
  }
//+------------------------------------------------------------------+ 